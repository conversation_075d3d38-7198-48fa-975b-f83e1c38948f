# Next.js Chocolate & Art Show

This directory contains the Next.js version of the Chocolate & Art Show website.

## Project Structure

The Next.js version will maintain the exact same visual design and functionality as the current static site while adding:

- Modern React components
- Server-side rendering
- Optimized performance
- Database integration with Convex
- Authentication with Clerk
- Seamless Vercel deployment

## Development Status

🚧 **Under Development** - Converting from static HTML/CSS to Next.js

## Current Static Site

The current working static site is located in the `features/partials` branch and can be viewed by running:

```bash
git checkout features/partials
npm run serve
```

## Next.js Migration

The Next.js version will be built incrementally following the task list in`../NEXTJS_CONVERSION_TASKS.md`.

## Design Preservation

All visual elements, animations, and user experience will be preserved exactly as they appear in the current static site.

## Getting Started

This directory is prepared for Next.js development. Follow Phase 1 of the conversion tasks to begin setup.
