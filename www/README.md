# Chocolate & Art Show Website

An immersive website for the Chocolate & Art Show Dallas event featuring art, music, and artisan chocolate. Built with a modern HTML partials system for maintainable, responsive design.

## 🎨 Features

- **Neon Sign Hero Section** with Monoton font and animated effects
- **Responsive Mobile Menu** with hamburger navigation
- **Interactive Gallery** with lightbox functionality
- **Event Information** and ticket integration
- **Artist Profiles** and submission forms
- **FAQ Section** with expandable content
- **Contact Forms** and social media integration
- **SEO Optimized** with structured data and meta tags
- **Accessibility Focused** with ARIA labels and semantic HTML

## 🛠️ Build System

This project uses a modern HTML partials system to maintain consistent headers and footers across all pages while enabling rapid development and easy maintenance.

### How It Works

The build system processes HTML files and replaces special include comments with the content of partial files:

```html
<!-- INCLUDE partials/header.html -->
<!-- INCLUDE partials/footer.html -->
```

## Quick Start

### 1. Install Dependencies (Optional)
```bash
npm install
```

### 2. Build the Site
```bash
npm run build
```

This creates a `dist/` folder with processed HTML files.

### 3. Development Mode
```bash
npm run watch
```

This watches for changes and rebuilds automatically.

### 4. Serve the Site
```bash
npm run serve
```

This serves the built site at `http://localhost:3000`.

### 5. Build and Serve
```bash
npm start
```

This builds and serves in one command.

## 📁 File Structure

```
www/
├── assets/
│   ├── css/
│   │   ├── main.css         # Main styles with neon effects
│   │   ├── base.css         # Base styles and reset
│   │   ├── layout.css       # Layout and grid systems
│   │   ├── components.css   # Reusable components
│   │   ├── buttons.css      # Button styles
│   │   ├── gallery.css      # Gallery and lightbox
│   │   └── tokens.css       # Design tokens and variables
│   ├── js/
│   │   ├── main.js          # Main JavaScript functionality
│   │   ├── gallery.js       # Gallery interactions
│   │   ├── confetti.js      # Confetti animations
│   │   └── compose.js       # Form handling
│   └── images/
│       ├── brand/           # Logo and brand assets
│       ├── gallery/         # Event photos
│       └── social/          # Social media icons
├── partials/
│   ├── header.html          # Navigation with mobile menu
│   ├── footer.html          # Footer with social links
│   └── cta-bar.html         # Call-to-action component
├── events/
│   └── dallas-tx-2025-09-18-19/  # Event-specific pages
├── schema/                  # JSON-LD structured data
├── index.html              # Homepage with neon hero
├── artists.html            # Artist profiles and submissions
├── contact.html            # Contact forms and info
├── faq.html               # Frequently asked questions
├── music.html             # Music and performance info
├── vendors.html           # Vendor information
├── build.js               # Build script
├── package.json           # NPM configuration
└── dist/                  # Generated files (after build)
```

## Making Changes

### To Update Navigation
1. Edit `partials/header.html`
2. Run `npm run build` or use `npm run watch`
3. All pages will automatically get the updated navigation

### To Update Footer
1. Edit `partials/footer.html`
2. Run `npm run build` or use `npm run watch`
3. All pages will automatically get the updated footer

### To Add New Pages
1. Create your HTML file with include directives:
   ```html
   <!DOCTYPE html>
   <html>
   <head>...</head>
   <body>
     <!-- INCLUDE partials/header.html -->

     <main>
       <!-- Your page content -->
     </main>

     <!-- INCLUDE partials/footer.html -->

     <script src="assets/js/main.js"></script>
   </body>
   </html>
   ```

2. Add the file to `build.js` in the `htmlFiles` array
3. Run `npm run build`

## 🎯 Technical Highlights

### Typography & Design
- **Monoton Font**: Google Fonts integration for distinctive neon signage
- **Responsive Design**: Mobile-first approach with breakpoints
- **CSS Grid & Flexbox**: Modern layout techniques
- **Custom Properties**: CSS variables for consistent theming
- **Animations**: Smooth transitions and neon glow effects

### Performance & SEO
- **Optimized Assets**: Compressed images and minified CSS
- **Structured Data**: JSON-LD schema for search engines
- **Meta Tags**: Complete OpenGraph and Twitter Card support
- **Accessibility**: WCAG 2.1 AA compliance focused
- **Fast Loading**: Preloaded critical resources

### Development Benefits

✅ **Single Source of Truth**: Update header/footer in one place
✅ **Consistency**: All pages use the same components
✅ **Easy Maintenance**: No need to update multiple files
✅ **Fast Development**: Watch mode rebuilds automatically
✅ **Simple**: No complex build tools, just Node.js
✅ **Mobile Menu**: Responsive navigation with hamburger menu
✅ **External CSS**: No inline styles, proper separation of concerns

## Build Script Features

- **Include Processing**: Replaces `<!-- INCLUDE path -->` with file content
- **Asset Copying**: Copies CSS, JS, images, and other assets
- **Watch Mode**: Automatically rebuilds on file changes
- **Error Handling**: Shows helpful error messages
- **Clean Builds**: Removes old files before building

## 🚀 Deployment

The `dist/` folder contains the final website ready for deployment. You can:

1. **Static Hosting**: Upload the entire `dist/` folder to services like Netlify, Vercel, or GitHub Pages
2. **Web Server**: Point your web server (Apache, Nginx) to serve files from `dist/`
3. **CDN**: Use the `dist/` folder with any CDN or static hosting service
4. **Local Testing**: Use `npm run serve` to test the built site locally

### Environment Setup
- Ensure all Google Fonts load properly (HTTPS required for production)
- Configure proper MIME types for `.svg` files
- Set up proper caching headers for assets
- Enable gzip compression for better performance

## 🔧 Troubleshooting

### Build Errors
- Check that partial files exist in `partials/` folder
- Ensure include paths are correct (relative to `partials/` folder)
- Verify HTML syntax in partial files
- Make sure `main.css` is properly copied to `dist/assets/css/`

### Font Loading Issues
- Verify Google Fonts URL is accessible (HTTPS required)
- Check browser console for font loading errors
- Ensure fallback fonts are working if Monoton fails to load

### Mobile Menu Problems
- Confirm `main.css` includes mobile menu styles
- Check that JavaScript is loading properly
- Verify viewport meta tag is present in all pages

### Missing Assets
- Ensure assets are in the correct folders (`assets/`, `events/`, `schema/`)
- Check that asset paths are relative to the site root
- Verify images are properly copied during build process

### Watch Mode Not Working
- Make sure you're editing the source files, not files in `dist/`
- Check that Node.js has permission to watch files
- Restart watch mode if needed: `Ctrl+C` then `npm run watch`

## 📝 Recent Updates

- **Monoton Font Integration**: Added Google Fonts Monoton for neon sign hero section
- **Mobile Menu Fix**: Resolved desktop visibility issues with responsive CSS
- **External CSS Migration**: Moved all inline styles to external CSS files
- **Build System Enhancement**: Improved partials processing and asset copying
- **Accessibility Improvements**: Enhanced ARIA labels and semantic structure

## 🤝 Contributing

1. Make changes to source files (not `dist/` files)
2. Test locally with `npm run serve`
3. Ensure mobile responsiveness works
4. Verify all fonts and assets load properly
5. Run build process before committing
6. Update this README if adding new features

---

**Event**: Chocolate & Art Show Dallas
**Dates**: September 18-19, 2025
**Venue**: Lofty Spaces, Dallas
**Website**: An immersive celebration of art, music, and artisan chocolate
