<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Events & Cities — Chocolate & Art Show</title>
    <meta name="description"
      content="Chocolate & Art Show events across different cities. Find dates, venues, and tickets for immersive art experiences." />

    <!-- Favicon + canonical -->
    <link rel="icon" type="image/png" href="../assets/images/brand/choco-logo.png" />
    <link rel="canonical" href="https://chocolateandartshow.com/events/" />

    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/tokens.css" />
    <link rel="stylesheet" href="../assets/css/base.css" />
    <link rel="stylesheet" href="../assets/css/layout.css" />
    <link rel="stylesheet" href="../assets/css/components.css" />
    <link rel="stylesheet" href="../assets/css/buttons.css" />
    <link rel="stylesheet" href="../assets/css/main.css" />

    <style>
      :root {
        --pink: #ff2ebd;
        --pink-2: #ff7ad6;
        --white: #ffffff;
        --bg: #000;
        --card-bg: #1a1a1a;
        --card-bd: #333;
        --fg: #eef2ff;
        --name: #fff;
      }

      body {
        background: var(--bg);
        color: var(--white);
        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        margin: 0;
        padding: 0;
        line-height: 1.6;
      }

      /* Container */
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
      }

      /* Navigation styles */
      .nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .nav-logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--white);
        text-decoration: none;
        font-weight: 800;
      }

      .nav-menu {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 2rem;
      }

      .nav-menu a {
        color: var(--white);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
      }

      .nav-menu a:hover,
      .nav-menu a[aria-current="page"] {
        color: var(--pink);
      }

      /* Main content spacing */
      main {
        padding-top: 120px;
        padding-bottom: 4rem;
      }

      /* Page header */
      .page-header {
        text-align: center;
        padding: 4rem 0;
      }

      .page-header h1 {
        font-size: clamp(2.5rem, 6vw, 4rem);
        font-weight: 800;
        margin-bottom: 1rem;
        color: var(--white);
        text-transform: uppercase;
        letter-spacing: 0.1em;
      }

      .page-header p {
        font-size: 1.2rem;
        color: var(--fg);
        max-width: 600px;
        margin: 0 auto;
      }

      /* City cards grid */
      .cities-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin: 4rem 0;
      }

      .city-card {
        background: linear-gradient(135deg, var(--card-bg), #2a2a2a);
        border: 2px solid var(--card-bd);
        border-radius: 0;
        padding: 2rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .city-card:hover {
        border-color: var(--pink);
        transform: translateY(-4px);
        box-shadow: 0 12px 30px rgba(255, 46, 189, 0.2);
      }

      .city-card__header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1.5rem;
      }

      .city-card__title {
        font-size: 1.8rem;
        font-weight: 700;
        margin: 0;
        color: var(--white);
      }

      .city-card__status {
        background: linear-gradient(135deg, var(--pink), var(--pink-2));
        color: var(--white);
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .city-card__details {
        margin-bottom: 2rem;
      }

      .city-card__detail {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
        color: var(--fg);
      }

      .city-card__detail strong {
        color: var(--white);
        margin-right: 0.5rem;
        min-width: 80px;
      }

      .city-card__actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
      }

      .btn {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        text-decoration: none;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border: 2px solid transparent;
        border-radius: 0;
        transition: all 0.3s ease;
        cursor: pointer;
        font-family: inherit;
        font-size: 0.9rem;
      }

      .btn-primary {
        background: linear-gradient(135deg, var(--pink), var(--pink-2));
        color: var(--white);
      }

      .btn-primary:hover {
        background: linear-gradient(135deg, var(--pink-2), var(--pink));
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 46, 189, 0.3);
      }

      .btn-secondary {
        background: transparent;
        color: var(--white);
        border-color: var(--card-bd);
      }

      .btn-secondary:hover {
        background: var(--white);
        color: var(--bg);
        border-color: var(--white);
        transform: translateY(-2px);
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .nav-menu {
          display: none;
        }

        .container {
          padding: 0 1rem;
        }

        .cities-grid {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }

        .city-card__actions {
          flex-direction: column;
        }
      }
    </style>
  </head>

  <body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only">Skip to main content</a>

    <!-- INCLUDE partials/header.html -->

    <!-- Main Content -->
    <main id="main-content">
      <div class="container">
        <section class="page-header">
          <h1>Events & Cities</h1>
          <p>Immersive art experiences coming to cities across the country. Find your city and secure your spot.</p>
        </section>

        <section class="cities-grid">
          <!-- Dallas Event Card -->
          <article class="city-card">
            <div class="city-card__header">
              <h2 class="city-card__title">Dallas, TX</h2>
              <span class="city-card__status">On Sale</span>
            </div>

            <div class="city-card__details">
              <div class="city-card__detail">
                <strong>Dates:</strong> September 18-19, 2025
              </div>
              <div class="city-card__detail">
                <strong>Venue:</strong> Lofty Spaces
              </div>
              <div class="city-card__detail">
                <strong>Address:</strong> 816 Montgomery St, Dallas, TX 75215
              </div>
              <div class="city-card__detail">
                <strong>Age:</strong> 21+ (ID Required)
              </div>
              <div class="city-card__detail">
                <strong>Doors:</strong> 7:00 PM both nights
              </div>
            </div>

            <div class="city-card__actions">
              <a href="/events/dallas-tx-2025-09-18-19/" class="btn btn-primary">Get Tickets</a>
              <a href="/events/dallas-tx-2025-09-18-19/" class="btn btn-secondary">Event Details</a>
            </div>
          </article>

          <!-- Future Cities (Coming Soon) -->
          <article class="city-card">
            <div class="city-card__header">
              <h2 class="city-card__title">Austin, TX</h2>
              <span class="city-card__status" style="background: #666;">Coming Soon</span>
            </div>

            <div class="city-card__details">
              <div class="city-card__detail">
                <strong>Dates:</strong> TBA 2025
              </div>
              <div class="city-card__detail">
                <strong>Venue:</strong> TBA
              </div>
              <div class="city-card__detail">
                <strong>Status:</strong> Planning in progress
              </div>
            </div>

            <div class="city-card__actions">
              <button
                onclick="openCompose('<EMAIL>', 'Austin Event Interest', 'I am interested in the Austin Chocolate & Art Show. Please notify me when tickets become available.')"
                class="btn btn-secondary">Notify Me</button>
            </div>
          </article>

          <article class="city-card">
            <div class="city-card__header">
              <h2 class="city-card__title">Houston, TX</h2>
              <span class="city-card__status" style="background: #666;">Coming Soon</span>
            </div>

            <div class="city-card__details">
              <div class="city-card__detail">
                <strong>Dates:</strong> TBA 2025
              </div>
              <div class="city-card__detail">
                <strong>Venue:</strong> TBA
              </div>
              <div class="city-card__detail">
                <strong>Status:</strong> Planning in progress
              </div>
            </div>

            <div class="city-card__actions">
              <button
                onclick="openCompose('<EMAIL>', 'Houston Event Interest', 'I am interested in the Houston Chocolate & Art Show. Please notify me when tickets become available.')"
                class="btn btn-secondary">Notify Me</button>
            </div>
          </article>
        </section>
      </div>
    </main>

    <!-- INCLUDE partials/footer.html -->

    <!-- Scripts -->
    <script src="../assets/js/compose.js"></script>
    <script src="../assets/js/main.js"></script>
  </body>

</html>