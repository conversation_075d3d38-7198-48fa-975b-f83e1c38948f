<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Dallas Event — Chocolate & Art Show | September 18-19, 2025</title>
    <meta name="description"
      content="Chocolate & Art Show Dallas at Lofty Spaces. September 18-19, 2025. Immersive art, live music, body painting, and artisan chocolate. 21+ event." />
    <meta name="keywords"
      content="Chocolate Art Show Dallas, September 2025, Lofty Spaces, art event, live music, body painting, 21+ event, tickets">
    <meta name="author" content="Chocolate & Art Show">

    <!-- OpenGraph Meta Tags -->
    <meta property="og:title" content="Dallas Event — Chocolate & Art Show | September 18-19, 2025">
    <meta property="og:description"
      content="Chocolate & Art Show Dallas at Lofty Spaces. September 18-19, 2025. Immersive art, live music, body painting, and artisan chocolate. 21+ event.">
    <meta property="og:type" content="event">
    <meta property="og:url" content="https://chocolateandartshow.com/events/dallas-tx-2025-09-18-19/">
    <meta property="og:image" content="https://chocolateandartshow.com/assets/images/gallery/hero-image.jpg">
    <meta property="og:site_name" content="Chocolate & Art Show">
    <meta property="event:start_time" content="2025-09-18T19:00:00-05:00">
    <meta property="event:end_time" content="2025-09-20T01:00:00-05:00">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@chocolateandartshow">
    <meta name="twitter:title" content="Dallas Event — Chocolate & Art Show | September 18-19, 2025">
    <meta name="twitter:description"
      content="Chocolate & Art Show Dallas at Lofty Spaces. September 18-19, 2025. Immersive art, live music, body painting, and artisan chocolate. 21+ event.">
    <meta name="twitter:image" content="https://chocolateandartshow.com/assets/images/gallery/hero-image.jpg">

    <!-- Favicon + canonical -->
    <link rel="icon" type="image/png" href="../../assets/images/brand/choco-logo.png" />
    <link rel="canonical" href="https://chocolateandartshow.com/events/dallas-tx-2025-09-18-19/" />

    <!-- CSS -->
    <link rel="stylesheet" href="../../assets/css/tokens.css" />
    <link rel="stylesheet" href="../../assets/css/base.css" />
    <link rel="stylesheet" href="../../assets/css/layout.css" />
    <link rel="stylesheet" href="../../assets/css/components.css" />
    <link rel="stylesheet" href="../../assets/css/buttons.css" />
    <link rel="stylesheet" href="../../assets/css/main.css" />

    <!-- JSON-LD Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Event",
      "name": "Chocolate & Art Show Dallas",
      "description": "Two nights of immersive art, live music, body painting, and artisan chocolate. A 21+ experience featuring local Dallas artists, live performances, interactive art, and a curated marketplace.",
      "url": "https://chocolateandartshow.com/events/dallas-tx-2025-09-18-19/",
      "startDate": "2025-09-18T19:00:00-05:00",
      "endDate": "2025-09-20T01:00:00-05:00",
      "eventStatus": "https://schema.org/EventScheduled",
      "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
      "location": {
        "@type": "Place",
        "name": "Lofty Spaces",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "816 Montgomery St",
          "addressLocality": "Dallas",
          "addressRegion": "TX",
          "postalCode": "75215",
          "addressCountry": "US"
        }
      },
      "organizer": {
        "@type": "Organization",
        "name": "Chocolate & Art Show",
        "url": "https://chocolateandartshow.com",
        "email": "<EMAIL>"
      },
      "offers": [
        {
          "@type": "Offer",
          "name": "General Admission - Single Night",
          "price": "35.00",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "url": "https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089"
        },
        {
          "@type": "Offer",
          "name": "VIP Experience - Single Night",
          "price": "75.00",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "url": "https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089"
        }
      ],
      "audience": {
        "@type": "Audience",
        "audienceType": "Adults 21+",
        "requiredMinAge": 21
      }
    }
    </script>

    <style>
      :root {
        --pink: #ff2ebd;
        --pink-2: #ff7ad6;
        --white: #ffffff;
        --bg: #000;
        --card-bg: #1a1a1a;
        --card-bd: #333;
        --fg: #eef2ff;
        --name: #fff;
      }

      body {
        background: var(--bg);
        color: var(--white);
        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        margin: 0;
        padding: 0;
        line-height: 1.6;
      }

      /* Container */
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
      }

      /* Navigation styles */
      .nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .nav-logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--white);
        text-decoration: none;
        font-weight: 800;
      }

      .nav-menu {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 2rem;
      }

      .nav-menu a {
        color: var(--white);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
      }

      .nav-menu a:hover,
      .nav-menu a[aria-current="page"] {
        color: var(--pink);
      }

      /* Main content spacing */
      main {
        padding-top: 120px;
        padding-bottom: 4rem;
      }

      /* Breadcrumbs */
      .breadcrumb {
        padding: 1rem 0;
        background: rgba(26, 26, 26, 0.5);
        border-bottom: 1px solid var(--card-bd);
      }

      .breadcrumb-list {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 0.5rem;
        align-items: center;
      }

      .breadcrumb-item {
        display: flex;
        align-items: center;
        color: var(--fg-secondary);
        font-size: 0.875rem;
      }

      .breadcrumb-item:not(:last-child)::after {
        content: '›';
        margin-left: 0.5rem;
        color: var(--card-bd);
      }

      .breadcrumb-item a {
        color: var(--pink-2);
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .breadcrumb-item a:hover {
        color: var(--pink);
      }

      .breadcrumb-item[aria-current="page"] {
        color: var(--fg);
        font-weight: 500;
      }

      /* Event header */
      .event-header {
        text-align: center;
        padding: 4rem 0;
        background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
        margin-bottom: 4rem;
      }

      .event-header h1 {
        font-size: clamp(2.5rem, 6vw, 4rem);
        font-weight: 800;
        margin-bottom: 1rem;
        color: var(--white);
        text-transform: uppercase;
        letter-spacing: 0.1em;
      }

      .event-header .event-subtitle {
        font-size: 1.5rem;
        color: var(--pink-2);
        margin-bottom: 2rem;
        font-weight: 600;
      }

      .event-header .event-description {
        font-size: 1.2rem;
        color: var(--fg);
        max-width: 800px;
        margin: 0 auto 3rem;
      }

      /* Ticket buttons */
      .ticket-section {
        display: flex;
        gap: 2rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 2rem;
      }

      .ticket-btn {
        display: inline-block;
        padding: 1.5rem 3rem;
        background: linear-gradient(135deg, var(--pink), var(--pink-2));
        color: var(--white);
        text-decoration: none;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border: 2px solid transparent;
        border-radius: 0;
        transition: all 0.3s ease;
        font-size: 1.1rem;
        text-align: center;
        min-width: 200px;
      }

      .ticket-btn:hover {
        background: linear-gradient(135deg, var(--pink-2), var(--pink));
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 46, 189, 0.3);
      }

      .urgency-text {
        color: var(--pink-2);
        font-weight: 600;
        font-size: 1rem;
        margin-top: 1rem;
      }

      /* Event details grid */
      .event-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 3rem;
        margin: 4rem 0;
      }

      .detail-card {
        background: linear-gradient(135deg, var(--card-bg), #2a2a2a);
        border: 2px solid var(--card-bd);
        border-radius: 0;
        padding: 2rem;
        transition: all 0.3s ease;
      }

      .detail-card:hover {
        border-color: var(--pink);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(255, 46, 189, 0.1);
      }

      .detail-card h3 {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: var(--white);
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .detail-card p,
      .detail-card ul {
        color: var(--fg);
        margin-bottom: 1rem;
      }

      .detail-card ul {
        padding-left: 1.5rem;
      }

      .detail-card li {
        margin-bottom: 0.5rem;
      }

      .detail-card strong {
        color: var(--white);
      }

      /* House rules section */
      .house-rules {
        background: linear-gradient(135deg, #2a1a1a, #3a2a2a);
        border: 2px solid #ff2ebd33;
        padding: 3rem;
        margin: 4rem 0;
        border-radius: 0;
      }

      .house-rules h2 {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 2rem;
        color: var(--pink-2);
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 0.1em;
      }

      .rules-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
      }

      .rule-item {
        background: rgba(255, 255, 255, 0.05);
        padding: 1.5rem;
        border-left: 4px solid var(--pink);
      }

      .rule-item h4 {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: var(--white);
      }

      .rule-item p {
        color: var(--fg);
        margin: 0;
        font-size: 0.95rem;
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .nav-menu {
          display: none;
        }

        .container {
          padding: 0 1rem;
        }

        .event-header {
          padding: 2rem 0;
        }

        .ticket-section {
          flex-direction: column;
          align-items: center;
        }

        .event-details {
          grid-template-columns: 1fr;
          gap: 2rem;
        }

        .rules-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>

  <body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only">Skip to main content</a>



    <!-- INCLUDE ../../partials/header.html -->
    <!-- Main Content -->
    <main id="main-content">
      <!-- Breadcrumbs -->
      <nav aria-label="Breadcrumb" class="breadcrumb">
        <div class="container">
          <ol class="breadcrumb-list">
            <li class="breadcrumb-item">
              <a href="/">Home</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/events/">Events</a>
            </li>
            <li class="breadcrumb-item" aria-current="page">
              Dallas September 2025
            </li>
          </ol>
        </div>
      </nav>

      <!-- Event Header -->
      <section class="event-header">
        <div class="container">
          <h1>Chocolate & Art Show</h1>
          <div class="event-subtitle">Dallas, Texas</div>
          <div class="event-description">
            An immersive experience celebrating art, live music, body painting, and artisan chocolate.
            Two unforgettable nights at Lofty Spaces in the heart of Dallas.
          </div>

          <!-- Cialdini-framed ticket CTAs -->
          <div class="ticket-section">
            <a href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089"
              class="ticket-btn" target="_blank" rel="noopener">
              Lock your spot — Thursday & Friday only, 21+
            </a>
          </div>
          <div class="urgency-text">
            Limited capacity • Advance purchase strongly recommended
          </div>
        </div>
      </section>

      <div class="container">
        <!-- Event Details -->
        <section class="event-details">
          <article class="detail-card">
            <h3>📅 Event Details</h3>
            <p><strong>Dates:</strong> September 18-19, 2025 (Thursday & Friday)</p>
            <p><strong>Doors Open:</strong> 7:00 PM both nights</p>
            <p><strong>Last Entry:</strong> 12:30 AM</p>
            <p><strong>Age Requirement:</strong> 21+ (Valid ID Required)</p>
            <p><strong>Dress Code:</strong> Come as you are! Creative expression encouraged.</p>
          </article>

          <article class="detail-card">
            <h3>📍 Venue Information</h3>
            <p><strong>Lofty Spaces</strong></p>
            <p>816 Montgomery St<br>Dallas, TX 75215</p>
            <p><strong>Parking:</strong> Available at venue (limited spaces)</p>
            <p><strong>Rideshare:</strong> Uber/Lyft recommended</p>
            <p><strong>Public Transit:</strong> Check Dallas transit for routes</p>
          </article>

          <article class="detail-card">
            <h3>🎨 What to Expect</h3>
            <ul>
              <li>Live art creation and installations</li>
              <li>Body painting performances</li>
              <li>Curated local music and DJs</li>
              <li>Artisan chocolate tastings</li>
              <li>Interactive art experiences</li>
              <li>Immersive lighting and atmosphere</li>
            </ul>
          </article>

          <article class="detail-card">
            <h3>🎫 Ticket Information</h3>
            <p><strong>Capacity:</strong> Limited intimate setting</p>
            <p><strong>Advance Purchase:</strong> Strongly recommended</p>
            <p><strong>No Re-entry:</strong> Once you leave, re-entry not permitted</p>
            <p><strong>Refunds:</strong> See Eventbrite policy</p>
            <p><strong>Questions:</strong>
              <button
                onclick="openCompose('<EMAIL>', 'Dallas Event Question', 'I have a question about the Dallas Chocolate & Art Show...')"
                style="background: none; border: none; color: var(--pink-2); text-decoration: underline; cursor: pointer; font-size: inherit; font-family: inherit;">
                Contact us
              </button>
            </p>
          </article>
        </section>

        <!-- House Rules -->
        <!-- <section class="house-rules">
          <h2>House Rules & Important Information</h2>
          <div class="rules-grid">
            <div class="rule-item">
              <h4>ID Required</h4>
              <p>This is a 21+ event. Valid government-issued photo ID is required for entry. No exceptions.</p>
            </div>

            <div class="rule-item">
              <h4>No Re-entry</h4>
              <p>Once you leave the venue, re-entry is not permitted. Please plan accordingly for the entire evening.
              </p>
            </div>

            <div class="rule-item">
              <h4>Respectful Behavior</h4>
              <p>Treat all artists, performers, staff, and fellow attendees with respect. Harassment will not be
                tolerated.</p>
            </div>

            <div class="rule-item">
              <h4>Photography</h4>
              <p>Personal photography is welcome. Please be respectful of performers and other guests when taking
                photos.</p>
            </div>

            <div class="rule-item">
              <h4>Capacity Limits</h4>
              <p>This is an intimate event with limited capacity. Advance ticket purchase is strongly recommended.</p>
            </div>

            <div class="rule-item">
              <h4>Safety First</h4>
              <p>Follow all venue safety guidelines. Report any concerns to staff immediately.</p>
            </div>
          </div>
        </section> -->

        <!-- Final CTA -->
        <section style="text-align: center; margin: 4rem 0;">
          <h2 style="font-size: 2rem; margin-bottom: 2rem; color: var(--white);">Ready to Experience Dallas?</h2>
          <div class="ticket-section">
            <a href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089"
              class="ticket-btn" target="_blank" rel="noopener">
              Get Your Tickets Now
            </a>
          </div>
          <p style="color: var(--fg); margin-top: 1rem;">
            Questions? <button
              onclick="openCompose('<EMAIL>', 'Dallas Event Question', 'I have a question about the Dallas Chocolate & Art Show...')"
              style="background: none; border: none; color: var(--pink-2); text-decoration: underline; cursor: pointer; font-size: inherit; font-family: inherit;">
              Contact us
            </button>
          </p>
        </section>
      </div>
    </main>

    <!-- INCLUDE ../../partials/footer.html -->

    <!-- Scripts -->
    <script src="../../assets/js/compose.js"></script>
    <script src="../../assets/js/main.js"></script>
  </body>

</html>