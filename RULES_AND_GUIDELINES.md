# Chocolate & Art Show - Development Rules & Guidelines

## Core Project Rules

### 1. Visual Design Preservation
- **NEVER** change the existing visual design, color scheme, or layout
- **ALWAYS** maintain the dark theme with neon pink accents (#ff2ebd, #ff7ad6)
- **PRESERVE** the Monoton font for hero sections and neon text effects
- **KEEP** all existing animations, hover effects, and visual interactions
- **MAINTAIN** the card-based layout system with 2px borders

### 2. Responsive Design Requirements
- **MOBILE-FIRST**: Always design and code for mobile first, then scale up
- **GALLERY RULE**: Never use more than 3 columns in gallery grids
- **BREAKPOINTS**: Maintain existing responsive breakpoints and behavior
- **TOUCH-FRIENDLY**: Ensure all interactive elements work well on touch devices

### 3. Accessibility Standards
- **WCAG 2.1 AA**: Maintain compliance throughout development
- **ARIA LABELS**: Keep all existing ARIA labels and add new ones as needed
- **SEMANTIC HTML**: Use proper HTML5 semantic elements
- **KEYBOARD NAVIGATION**: Ensure all features work with keyboard only
- **SCREEN READERS**: Test with screen reader compatibility

### 4. Technology Stack Constraints
- **NEXT.JS**: Use Next.js 14+ with App Router (not Pages Router)
- **CONVEX**: Use Convex for all database operations and real-time features
- **CLERK**: Use Clerk for all authentication and user management
- **VERCEL**: Optimize for Vercel deployment
- **CSS**: Preserve existing CSS, migrate gradually to CSS Modules if needed

## Development Guidelines

### Code Organization
- Keep Next.js files in separate `nextjs/` directory during conversion
- Maintain clear separation between components, pages, and utilities
- Use TypeScript for new code (gradually migrate existing JavaScript)
- Follow Next.js file-based routing conventions

### Component Development
- Convert existing partials to React components with same functionality
- Maintain component reusability and single responsibility principle
- Use proper prop types and default values
- Implement error boundaries for robust error handling

### Performance Requirements
- Optimize images using Next.js Image component
- Implement proper loading states and skeleton screens
- Use React.memo() for expensive components
- Minimize bundle size and implement code splitting

### Database Integration
- Use Convex queries and mutations for all data operations
- Implement proper error handling for database operations
- Use optimistic updates where appropriate
- Maintain data consistency and validation

### Authentication Flow
- Implement Clerk authentication with proper route protection
- Handle user sessions and token management
- Provide clear login/logout flows
- Implement role-based access control for admin features

## Feature Implementation Rules

### Artist Submission System
- Create intuitive multi-step form with file upload capability
- Implement proper validation and error messaging
- Store submissions in Convex with proper data structure
- Send confirmation emails using Clerk/Convex integration

### Ticket Purchasing
- Integrate with payment processor (Stripe recommended)
- Implement secure checkout flow
- Store order data in Convex
- Send confirmation emails and tickets

### Admin Dashboard
- Create protected admin routes
- Implement CRUD operations for events, artists, vendors
- Provide analytics and reporting features
- Maintain audit logs for admin actions

## Content and Messaging Rules

### Brand Voice
- Maintain immersive, artistic, and sophisticated tone
- Focus on Dallas local community and art scene
- Emphasize the unique chocolate and art combination
- Keep messaging inclusive and welcoming

### Event Information
- Always display correct event details: September 18-19, 2025
- Venue: Lofty Spaces, Dallas
- Maintain 21+ age requirement messaging
- Keep ticket pricing and availability current

## Quality Assurance

### Testing Requirements
- Test all features on mobile devices and desktop
- Verify accessibility with screen readers
- Test with different user roles (guest, artist, admin)
- Validate all forms and error states
- Test payment flows in sandbox mode

### Browser Compatibility
- Support modern browsers (Chrome, Firefox, Safari, Edge)
- Ensure graceful degradation for older browsers
- Test on both iOS and Android devices
- Verify performance on slower connections

## Deployment and Maintenance

### Version Control
- Use descriptive commit messages
- Create feature branches for all new development
- Require code review for main branch merges
- Tag releases with semantic versioning

### Environment Management
- Maintain separate development, staging, and production environments
- Use environment variables for all configuration
- Never commit sensitive data or API keys
- Test deployments in staging before production

### Monitoring and Analytics
- Implement error tracking and monitoring
- Track user interactions and conversion funnels
- Monitor site performance and Core Web Vitals
- Set up alerts for critical issues

## Communication and Documentation

### Code Documentation
- Document all complex functions and components
- Maintain up-to-date README files
- Document API endpoints and data structures
- Keep deployment and setup instructions current

### Progress Tracking
- Update task lists and project status regularly
- Communicate blockers and dependencies clearly
- Document decisions and architectural choices
- Maintain changelog for releases

## Prohibited Actions

### Design Changes
- **NEVER** change the color scheme or brand colors
- **NEVER** modify the Monoton font usage or neon effects
- **NEVER** alter the card-based layout system
- **NEVER** change the mobile-first responsive approach

### Technical Constraints
- **NEVER** use outdated React patterns (class components, etc.)
- **NEVER** bypass Clerk for authentication
- **NEVER** use direct database access instead of Convex
- **NEVER** implement features that break accessibility

### Content Restrictions
- **NEVER** change event dates or venue information without approval
- **NEVER** modify the 21+ age requirement
- **NEVER** alter the brand messaging or tone
- **NEVER** remove existing social media links or contact information
