# Chocolate & Art Show - Project Memory

## Project Overview
An immersive website for the Chocolate & Art Show Dallas event featuring art, music, and artisan chocolate. The site celebrates creativity through an interactive, accessible, and visually stunning experience.

## Current Tech Stack
- **Frontend**: HTML5, CSS3, JavaScript
- **Build System**: Custom Node.js partials system
- **Styling**: CSS Custom Properties, Mobile-first responsive design
- **Fonts**: Monoton (Google Fonts) for hero sections
- **Deployment**: Static files ready for any hosting

## Planned Tech Stack (Next.js Migration)
- **Framework**: Next.js 14+ (App Router)
- **Database**: Convex (real-time, serverless)
- **Authentication**: Clerk (user management, auth)
- **Deployment**: Vercel (seamless Next.js integration)
- **Styling**: Preserve existing CSS, migrate to CSS Modules/Tailwind gradually

## Design System & Brand Identity

### Color Palette
- **Primary Background**: #000000 (Pure black)
- **Primary Pink**: #ff2ebd (Neon pink)
- **Secondary Pink**: #ff7ad6 (Lighter pink)
- **Text Primary**: #ffffff (White)
- **Text Secondary**: #eef2ff (Light blue-white)
- **Card Background**: #1a1a1a (Dark gray)
- **Card Borders**: #333333 (Medium gray)

### Typography
- **Hero Font**: Monoton (Google Fonts) - Neon sign aesthetic
- **Body Font**: System font stack for performance
- **Font Sizes**: Clamp() functions for responsive scaling

### Layout Principles
- **Mobile-first**: All designs start with mobile, scale up
- **Card-based**: 2px borders, hover effects, consistent spacing
- **Grid Systems**: CSS Grid and Flexbox, max 3 columns in galleries
- **Accessibility**: WCAG 2.1 AA compliance, ARIA labels, semantic HTML

## Current Site Structure

### Pages
- **Homepage** (`index.html`): Hero with neon effects, event info, gallery
- **Artists** (`artists.html`): Artist profiles and submission forms
- **Contact** (`contact.html`): Venue info, contact forms, directions
- **FAQ** (`faq.html`): Expandable Q&A sections
- **Events/Dallas** (`events/dallas-tx-2025-09-18-19/`): Ticket sales, event details

### Components (Current Partials)
- **Header** (`partials/header.html`): Navigation with mobile menu
- **Footer** (`partials/footer.html`): Social links, event info, celebrate button

### Key Features
- **Neon Hero Section**: Animated text effects with Monoton font
- **Interactive Gallery**: Lightbox functionality, responsive grid
- **Mobile Menu**: Hamburger navigation with overlay
- **Social Icons**: White outlined SVG icons with hover effects
- **Confetti Animation**: Celebrate button with canvas animation

## Event Details
- **Event**: Chocolate & Art Show Dallas
- **Dates**: September 18-19, 2025 (Thursday & Friday)
- **Venue**: Lofty Spaces, 816 Montgomery St, Dallas, TX 75215
- **Age**: 21+ event (ID required)
- **Doors**: 7:00 PM both nights
- **Capacity**: Limited, intimate setting

## Future Features Roadmap

### Phase 1: Next.js Foundation
- Convert HTML pages to Next.js pages
- Transform partials to React components
- Preserve all existing styling and functionality
- Fix navigation routing issues

### Phase 2: Authentication & User Management
- Integrate Clerk for user authentication
- User profiles for artists and attendees
- Protected routes for submissions and purchases

### Phase 3: Database Integration
- Convex setup for real-time data
- Artist submission forms with file uploads
- Event management system
- Vendor application system

### Phase 4: E-commerce & Ticketing
- Ticket purchasing integration
- Payment processing
- Order management
- Email confirmations

### Phase 5: Admin Dashboard
- Event management interface
- Artist/vendor approval system
- Analytics and reporting
- Content management

### Phase 6: Advanced Features
- Real-time event updates
- Push notifications
- Social media integration
- Advanced analytics

## User Experience Priorities
1. **Performance**: Fast loading, optimized images
2. **Accessibility**: Screen reader friendly, keyboard navigation
3. **Mobile Experience**: Touch-friendly, responsive design
4. **Visual Impact**: Maintain neon aesthetic, smooth animations
5. **Usability**: Intuitive navigation, clear CTAs

## Development Guidelines
- Maintain existing visual design 100%
- Keep mobile-first approach
- Preserve accessibility features
- Use semantic HTML structure
- Follow React best practices for new components
- Implement proper error handling
- Ensure SEO optimization

## Content Strategy
- Immersive art and chocolate experience messaging
- Local Dallas community focus
- Artist and vendor spotlight features
- Event photography and social proof
- Clear ticket purchasing flow
