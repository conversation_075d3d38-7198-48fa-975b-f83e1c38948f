# Next.js Conversion Branch

This branch contains the Next.js conversion workspace for the Chocolate & Art Show website.

## Branch Purpose

This `features/nextjs-conversion` branch is dedicated to:

- Next.js development and migration
- Documentation for the conversion process
- Clean workspace for modern React development

## Contents

- `nextjs/` - Next.js application directory (development workspace)
- `PROJECT_MEMORY.md` - Complete project context and requirements
- `RULES_AND_GUIDELINES.md` - Development standards and constraints
- `NEXTJS_CONVERSION_TASKS.md` - Detailed conversion task list

## Current Static Site

The working static site is preserved in the `features/partials` branch:

```bash
git checkout features/partials
npm run serve
```

## Next Steps

Begin Phase 1 of the Next.js conversion following the tasks in `NEXTJS_CONVERSION_TASKS.md`:

1. Set up Next.js project in `nextjs/` directory
2. Install dependencies and configure TypeScript
3. Migrate assets and styling
4. Convert HTML pages to React components

## Design Requirements

- Preserve exact visual design from static site
- Maintain dark theme with neon pink accents
- Keep mobile-first responsive approach
- Ensure accessibility standards (WCAG 2.1 AA)

## Tech Stack

- **Framework**: Next.js 14+ with App Router
- **Database**: Convex (real-time, serverless)
- **Authentication**: Clerk
- **Deployment**: Vercel
- **Styling**: Preserve existing CSS, migrate gradually
